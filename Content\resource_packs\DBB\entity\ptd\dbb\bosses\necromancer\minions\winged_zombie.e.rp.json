{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "ptd_dbb:winged_zombie", "min_engine_version": "1.16.0", "materials": {"default": "entity_alphatest", "circle": "entity_emissive_alpha", "light": "emissive_translucent"}, "textures": {"default": "textures/ptd/dbb/entity/bosses/necromancer/minions/winged_zombie"}, "geometry": {"default": "geometry.ptd_dbb_winged_zombie"}, "animations": {"look_at_target": "animation.common.look_at_target", "spawn": "animation.ptd_dbb_winged_zombie.spawn", "idle": "animation.ptd_dbb_winged_zombie.idle", "move": "animation.ptd_dbb_winged_zombie.move", "attack": "animation.ptd_dbb_winged_zombie.attack", "death": "animation.ptd_dbb_winged_zombie.death", "no_effects": "animation.ptd_dbb_winged_zombie.no_effects", "general": "controller.animation.ptd_dbb_winged_zombie.general"}, "scripts": {"should_update_bones_and_effects_offscreen": true, "animate": ["general", {"look_at_target": "q.property('ptd_dbb:spawning') == false && q.property('ptd_dbb:dead') == false"}, {"no_effects": "q.property('ptd_dbb:spawning') == false && q.property('ptd_dbb:dead') == false"}]}, "render_controllers": ["controller.render.ptd_dbb_winged_zombie"], "spawn_egg": {"base_color": "#6a9c5a", "overlay_color": "#b6c7c7"}}}}