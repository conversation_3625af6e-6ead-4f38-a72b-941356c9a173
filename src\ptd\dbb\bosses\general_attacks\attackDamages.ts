/**
 * Interface for attack damage configuration
 */
interface AttackDamage {
  /**
   * Direct damage value to apply
   */
  damage: number;
}

/**
 * Interface for Piglin Champion attack damages
 */
interface PiglinChampionAttackDamages {
  horizontal: AttackDamage;
  vertical: {
    axe: AttackDamage;
    rocks: AttackDamage;
  };
  foot_stomp: AttackDamage;
  spin_slam: AttackDamage;
  body_slam: AttackDamage;
  charging: AttackDamage;
  summoning_chant?: AttackDamage; // Optional as it might not deal damage
}

/**
 * Interface for Necromancer attack damages
 */
interface NecromancerAttackDamages {
  cataclysm: AttackDamage;
  soul_drain: AttackDamage;
  // Add other attacks as they are implemented
}

interface GrimhowlAttackDamages {
  left_claw: AttackDamage;
  right_claw: AttackDamage;
  backstep_sword: AttackDamage;
  pounce: AttackDamage;
  shadow_onslaught: AttackDamage;
  slash: AttackDamage;
  spinning_slash: AttackDamage;
  roar: AttackDamage;
  collision_damage: AttackDamage; // Not all attacks have collision damage
}

/**
 * Piglin Champion attack damages as direct values
 */
export const PIGLIN_CHAMPION_ATTACK_DAMAGES: Piglin<PERSON>hampionAttackDamages = {
  horizontal: { damage: 16 },
  vertical: {
    axe: { damage: 16 },
    rocks: { damage: 8 }
  },
  foot_stomp: { damage: 12 },
  spin_slam: { damage: 12 },
  body_slam: { damage: 18 },
  charging: { damage: 20 }
};

/**
 * Necromancer attack damages as direct values
 */
export const NECROMANCER_ATTACK_DAMAGES: NecromancerAttackDamages = {
  cataclysm: { damage: 6 },
  soul_drain: { damage: 7 }
};

/**
 * Grimhowl attack damages as direct values
 */
export const GRIMHOWL_ATTACK_DAMAGES: GrimhowlAttackDamages = {
  left_claw: { damage: 4 },
  right_claw: { damage: 4 },
  backstep_sword: { damage: 5 },
  pounce: { damage: 8 },
  shadow_onslaught: { damage: 8 },
  slash: { damage: 8 },
  spinning_slash: { damage: 6 },
  roar: { damage: 2 },
  collision_damage: { damage: 4 }
};