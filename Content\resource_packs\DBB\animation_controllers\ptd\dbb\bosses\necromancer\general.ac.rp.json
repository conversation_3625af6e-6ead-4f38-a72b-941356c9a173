{"format_version": "1.10.0", "animation_controllers": {"controller.animation.ptd_dbb_necromancer.general": {"states": {"default": {"animations": ["spawn"], "transitions": [{"idling": "q.property('ptd_dbb:spawning') == false || q.all_animations_finished"}], "blend_transition": 0.3}, "idling": {"animations": ["idle"], "transitions": [{"walking": "q.ground_speed > 0.3"}, {"dead": "q.property('ptd_dbb:dead') == true"}, {"cataclysm": "q.property('ptd_dbb:attack') == 'cataclysm' && q.property('ptd_dbb:cooling_down') == false"}, {"book_of_the_damned": "q.property('ptd_dbb:attack') == 'book_of_the_damned' && q.property('ptd_dbb:cooling_down') == false"}, {"soul_drain": "q.property('ptd_dbb:attack') == 'soul_drain' && q.property('ptd_dbb:cooling_down') == false"}, {"phantom_phase_start": "q.property('ptd_dbb:attack') == 'phantom_phase_start'"}, {"undead_summon": "q.property('ptd_dbb:attack') == 'undead_summon' && q.property('ptd_dbb:cooling_down') == false"}, {"arcane_blast": "q.property('ptd_dbb:attack') == 'arcane_blast' && q.property('ptd_dbb:cooling_down') == false"}, {"soul_hands": "q.property('ptd_dbb:attack') == 'soul_hands' && q.property('ptd_dbb:cooling_down') == false"}, {"soul_trap": "q.property('ptd_dbb:attack') == 'soul_trap' && q.property('ptd_dbb:cooling_down') == false"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "walking": {"animations": ["move"], "transitions": [{"idling": "q.ground_speed < 0.3"}, {"dead": "q.property('ptd_dbb:dead') == true"}, {"cataclysm": "q.property('ptd_dbb:attack') == 'cataclysm' && q.property('ptd_dbb:cooling_down') == false"}, {"soul_drain": "q.property('ptd_dbb:attack') == 'soul_drain' && q.property('ptd_dbb:cooling_down') == false"}, {"phantom_phase_start": "q.property('ptd_dbb:attack') == 'phantom_phase_start'"}, {"undead_summon": "q.property('ptd_dbb:attack') == 'undead_summon' && q.property('ptd_dbb:cooling_down') == false"}, {"arcane_blast": "q.property('ptd_dbb:attack') == 'arcane_blast' && q.property('ptd_dbb:cooling_down') == false"}, {"soul_hands": "q.property('ptd_dbb:attack') == 'soul_hands' && q.property('ptd_dbb:cooling_down') == false"}, {"soul_trap": "q.property('ptd_dbb:attack') == 'soul_trap' && q.property('ptd_dbb:cooling_down') == false"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "cataclysm": {"animations": ["cataclysm"], "transitions": [{"idling": "q.property('ptd_dbb:attack') == 'none' || q.all_animations_finished"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "book_of_the_damned": {"animations": ["book_of_the_damned"], "transitions": [{"idling": "q.property('ptd_dbb:attack') == 'none' || q.all_animations_finished"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "soul_drain": {"animations": ["soul_drain"], "transitions": [{"idling": "q.property('ptd_dbb:attack') == 'none' || q.all_animations_finished"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "phantom_phase_start": {"animations": ["phantom_phase_start"], "transitions": [{"phantom_phase_end": "q.property('ptd_dbb:attack') == 'phantom_phase_end'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "phantom_phase_end": {"animations": ["phantom_phase_end"], "transitions": [{"idling": "q.property('ptd_dbb:attack') == 'none' && q.all_animations_finished"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": {"0.0": 1, "0.0833": 0.96571, "0.1667": 0.8738, "0.25": 0.74074, "0.3333": 0.58299, "0.4167": 0.41701, "0.5": 0.25926, "0.5833": 0.1262, "0.6667": 0.03429, "0.75": 0}, "blend_via_shortest_path": true}, "undead_summon": {"animations": ["undead_summon"], "transitions": [{"idling": "q.property('ptd_dbb:attack') == 'none' || q.all_animations_finished"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "arcane_blast": {"animations": ["arcane_blast"], "transitions": [{"idling": "q.property('ptd_dbb:attack') == 'none' || q.all_animations_finished"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "soul_hands": {"animations": ["soul_hands"], "transitions": [{"idling": "q.property('ptd_dbb:attack') == 'none' || q.all_animations_finished"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "soul_trap": {"animations": ["soul_trap"], "transitions": [{"idling": "q.property('ptd_dbb:attack') == 'none' || q.all_animations_finished"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3, "blend_via_shortest_path": true}, "dead": {"animations": ["death"]}}}}}